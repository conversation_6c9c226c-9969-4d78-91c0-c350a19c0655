{"name": "get-suno-lyric", "version": "1.0.2", "description": "A tiny browser extension to get suno lyric", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "tsc": "tsc", "dev": "rspack build --watch --mode development", "build": "rspack build --mode production", "zip": "pnpm build && node scripts/zip.js"}, "keywords": ["browser", "extension", "chrome"], "author": "zhanghe.dev", "license": "MIT", "packageManager": "pnpm@10.6.5+sha512.cdf928fca20832cd59ec53826492b7dc25dc524d4370b6b4adbf65803d32efaa6c1c88147c0ae4e8d579a6c9eec715757b50d4fa35eea179d868eada4ed043af", "devDependencies": {"@rspack/cli": "^1.3.9", "@rspack/core": "^1.3.9", "@tailwindcss/postcss": "^4.1.6", "@types/react": "^19.1.3", "@types/react-dom": "^19.1.3", "chrome-types": "^0.1.349", "css-loader": "^7.1.2", "postcss": "^8.5.3", "postcss-loader": "^8.1.1", "tailwindcss": "^4.1.6", "ts-loader": "^9.5.2", "typescript": "^5.8.3"}, "dependencies": {"archiver": "^7.0.1", "react": "^19.1.0", "react-dom": "^19.1.0"}, "pnpm": {"onlyBuiltDependencies": ["@tailwindcss/oxide"]}}