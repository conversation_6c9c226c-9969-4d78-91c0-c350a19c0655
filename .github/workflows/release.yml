name: Release Chrome Extension

on:
  push:
    tags:
      - 'v*' # 触发条件：推送以 v 开头的tag

jobs:
  build-and-publish:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # 获取完整的 git 历史用于版本号
          
      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'pnpm'

      - name: Get version from git tag
        id: get_version
        run: echo "VERSION=${GITHUB_REF#refs/tags/v}" >> $GITHUB_ENV

      - name: Update version in manifest.json
        run: |
          sed -i "s/\"version\": \".*\"/\"version\": \"$VERSION\"/" src/manifest.json
          sed -i "s/\"version\": \".*\"/\"version\": \"$VERSION\"/" package.json

      - name: Install dependencies
        run: pnpm install

      - name: Create ZIP file
        run: pnpm zip

      - name: Upload to Chrome Web Store
        uses: mnao305/chrome-extension-upload@v5.0.0
        with:
          file-path: Tiny-helmet.zip
          extension-id: ${{ secrets.CHROME_EXTENSION_ID }}
          client-id: ${{ secrets.CHROME_CLIENT_ID }}
          client-secret: ${{ secrets.CHROME_CLIENT_SECRET }}
          refresh-token: ${{ secrets.CHROME_REFRESH_TOKEN }}
          publish: true

      - name: Create GitHub Release
        uses: softprops/action-gh-release@v1
        with:
          files: Tiny-helmet.zip
          name: Release ${{ env.VERSION }}
          body: |
            Release version ${{ env.VERSION }}
            
            Changes in this version:
            - Please check the commit history for detailed changes
          draft: false
          prerelease: false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
